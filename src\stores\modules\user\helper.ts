import type { IUserInfo } from '@/types/user'

export class UserInfo implements IUserInfo {
  userId: string
  userName: string
  userStatus: string
  isPassword: boolean
  nickName: string
  avatarUrl: string
  corpList: Array<{
    corpId: string
    corpName: string
    realName: string
    corpLogo: string
    corp: any
    id: string
    isDefault: boolean
  }>
  memberInfo?: {
    id: string
    corpId: string
    corp: {
      id: string
      corpName: string
      corpStatus: string
      corpCode: string
      creatorId: string
      createTime: string
    }
    userId: string
    user: IUserInfo
    memberStatus: string
    memberRole: string
    createTime: string
    realName?: string
  }
  constructor() {
    this.userId = ''
    this.userName = ''
    this.userStatus = ''
    this.isPassword = false
    this.nickName = ''
    this.avatarUrl = ''
    this.memberInfo = undefined
    this.corpList = []
  }
}
