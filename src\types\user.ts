export interface IUserInfo {
  userId: string
  userName: string
  userStatus: string
  isPassword: boolean
  nickName: string
  avatarUrl: string
  corpList: Array<{
    id: string
    corpId: string
    corpName: string
    corpLogo: string
    corp: any
    isDefault: boolean
  }>
  memberInfo?: {
    id: string
    corpId: string
    corp: {
      id: string
      corpName: string
      corpStatus: string
      corpCode: string
      creatorId: string
      createTime: string
    }
    userId: string
    user: IUserInfo
    memberStatus: string
    memberRole: string
    createTime: string
    realName?: string // Add realName property for backward compatibility
  }
}

export interface IGetQrCodeResponse {
  qrCode: string
  qrCodeId: string
}

export interface IGetQrCodeResult {
  scanStatus: number
  token: string | null
}

export interface IgetCurrentUserInfoResponse {
  userInfo: IUserInfo
  memberInfo: {
    id: string
    corpId: string
    corp: {
      id: string
      corpName: string
      corpStatus: string
      corpCode: string
      creatorId: string
      createTime: string
    }
    userId: string
    user: IUserInfo
    memberStatus: string
    memberRole: string
    createTime: string
  }
}
